"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { useUserStore } from "@/stores/userStore"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  sendEmailVerification,
  verifyEmailCode,
  registerWithVerification,
  registerDirect,
  updateUserProfile,
  completeRegistration,
  skipProfileUpdate
} from "@/actions"
import Image from "next/image"

type RegistrationStep = 'form' | 'verification' | 'profile-update'

export default function RegisterPage() {
  // Form data
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [firstName, setFirstName] = useState("")
  const [lastName, setLastName] = useState("")
  const [phone, setPhone] = useState("")
  const [gender, setGender] = useState<'male' | 'female' | 'other'>('male')
  const [role, setRole] = useState<'tenant' | 'landlord'>('tenant')

  // Profile update data
  const [bio, setBio] = useState("")
  const [dateOfBirth, setDateOfBirth] = useState("")

  // UI state
  const [currentStep, setCurrentStep] = useState<RegistrationStep>('form')
  const [verificationCode, setVerificationCode] = useState("")

  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [isDevelopmentMode, setIsDevelopmentMode] = useState(false)


  const router = useRouter()

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")

    if (password !== confirmPassword) {
      setError("Mật khẩu không khớp!")
      return
    }

    if (!email || !password || !firstName || !lastName || !phone) {
      setError("Vui lòng điền đầy đủ thông tin!")
      return
    }

    setIsLoading(true)

    try {
      if (isDevelopmentMode) {
        // Direct registration for development
        const authResponse = await registerDirect({
          email,
          password,
          firstName,
          lastName,
          phone,
          gender,
          role,
        })

        // Convert to user store format and login
        const user = {
          id: authResponse.user.id,
          firstName: authResponse.user.firstName,
          lastName: authResponse.user.lastName,
          email: authResponse.user.email,
          phone: authResponse.user.phone,
          gender: authResponse.user.gender,
          role: authResponse.user.role,
          bio: authResponse.user.bio,
          dateOfBirth: authResponse.user.dateOfBirth,
          avatar: authResponse.user.avatar,
          createdAt: authResponse.user.createdAt,
          updatedAt: authResponse.user.updatedAt,
        }

        useUserStore.setState({ user, isAuthenticated: true })

        // Redirect to dashboard
        if (role === 'tenant') {
          router.push("/dashboard/tenant")
        } else {
          router.push("/dashboard/landlord")
        }
      } else {
        // Send verification email
        await sendEmailVerification(email)
        setCurrentStep('verification')
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Đăng ký thất bại';
      setError(errorMessage);
    } finally {
      setIsLoading(false)
    }
  }

  const handleVerificationSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")

    if (!verificationCode) {
      setError("Vui lòng nhập mã xác thực!")
      return
    }

    setIsLoading(true)

    try {
      // Verify email code
      const verifyResponse = await verifyEmailCode(email, verificationCode)

      if (verifyResponse.verificationToken) {
        // Register with verification token
        const authResponse = await registerWithVerification({
          email,
          password,
          firstName,
          lastName,
          phone,
          gender,
          role,
        }, verifyResponse.verificationToken)

        // Convert to user store format and login
        const user = {
          id: authResponse.user.id,
          firstName: authResponse.user.firstName,
          lastName: authResponse.user.lastName,
          email: authResponse.user.email,
          phone: authResponse.user.phone,
          gender: authResponse.user.gender,
          role: authResponse.user.role,
          bio: authResponse.user.bio,
          dateOfBirth: authResponse.user.dateOfBirth,
          avatar: authResponse.user.avatar,
          createdAt: authResponse.user.createdAt,
          updatedAt: authResponse.user.updatedAt,
        }

        useUserStore.setState({ user, isAuthenticated: true })

        // Move to profile update step instead of redirecting directly
        setCurrentStep('profile-update')
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Xác thực thất bại';
      setError(errorMessage);
    } finally {
      setIsLoading(false)
    }
  }

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Update profile with additional information
      await updateUserProfile({
        firstName,
        lastName,
        phone,
        gender,
        bio,
        dateOfBirth,
      })

      // Use server action for navigation
      const formData = new FormData()
      formData.append('role', role)
      await completeRegistration(formData)
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Cập nhật thông tin thất bại';
      setError(errorMessage);
      setIsLoading(false)
    }
  }

  const handleSkipProfileUpdate = async () => {
    // Use server action for navigation
    await skipProfileUpdate()
  }

  const resendVerification = async () => {
    setIsLoading(true)
    setError("")

    try {
      await sendEmailVerification(email)
      // You might want to show a success message here
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Gửi lại mã thất bại';
      setError(errorMessage);
    } finally {
      setIsLoading(false)
    }
  }



  return (
    <div className="min-h-screen bg-gray-50 flex">
      <div className="flex w-full max-w-7xl mx-auto">
        {/* Left Side - Marketing Content */}
        <div className="flex-1 bg-gradient-to-br from-green-400 to-green-600 flex flex-col justify-center items-center p-12 relative overflow-hidden">
          {/* Background Image */}
          <div className="absolute inset-0 flex items-center justify-center">
            <Image
              src="/goal.png"
              alt="Goal"
              width={400}
              height={400}
              className="object-contain opacity-20"
            />
          </div>

          {/* Content */}
          <div className="relative z-10 text-white text-center max-w-md">
            <h1 className="text-4xl font-bold mb-4">
              Join the <span className="text-yellow-300">Largest & Leading</span>
            </h1>
            <p className="text-xl mb-8">Global Agri & Food B2B Marketplace</p>

            {/* Stats */}
            <div className="grid grid-cols-2 gap-6 mb-8">
              <div className="bg-white bg-opacity-20 rounded-lg p-4">
                <div className="text-2xl font-bold">3M+</div>
                <div className="text-sm">SUPPLIERS</div>
              </div>
              <div className="bg-white bg-opacity-20 rounded-lg p-4">
                <div className="text-2xl font-bold">2M+</div>
                <div className="text-sm">GLOBAL BUYERS</div>
              </div>
              <div className="bg-white bg-opacity-20 rounded-lg p-4">
                <div className="text-2xl font-bold">1,000+</div>
                <div className="text-sm">DAILY BUYING REQUESTS</div>
              </div>
              <div className="bg-white bg-opacity-20 rounded-lg p-4">
                <div className="text-2xl font-bold">200+</div>
                <div className="text-sm">COUNTRIES</div>
              </div>
            </div>

            <button className="bg-white text-green-600 font-bold py-3 px-8 rounded-lg hover:bg-gray-100 transition-colors">
              POST YOUR BUYING REQUEST NOW
            </button>
          </div>
        </div>

        {/* Right Side - Registration Form */}
        <div className="flex-1 bg-white flex items-center justify-center p-8">
          <div className="w-full max-w-md">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                {currentStep === 'verification'
                  ? 'Xác Thực Email'
                  : currentStep === 'profile-update'
                  ? 'Cập Nhật Thông Tin'
                  : 'Sign Up And Start Contacting Buyers Today!'
                }
              </h2>
              <p className="text-gray-600 text-sm">
                {currentStep === 'verification'
                  ? `Nhập mã xác thực đã gửi đến ${email}`
                  : currentStep === 'profile-update'
                  ? 'Hoàn thiện thông tin cá nhân của bạn'
                  : 'Your Contact Details'
                }
              </p>
            </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          )}

          {currentStep === 'form' ? (
            <form className="space-y-4" onSubmit={handleFormSubmit}>
              {/* User Type Selection */}
              <div className="flex gap-4 mb-6">
                <label className="flex items-center space-x-2 flex-1">
                  <input
                    type="radio"
                    name="role"
                    value="tenant"
                    checked={role === 'tenant'}
                    onChange={(e) => setRole(e.target.value as 'tenant' | 'landlord')}
                    className="text-green-600 focus:ring-green-500"
                    disabled={isLoading}
                  />
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <span className="text-green-600 text-sm">👤</span>
                    </div>
                    <div>
                      <div className="text-sm font-medium">I&apos;m a supplier, manufacturer &</div>
                      <div className="text-sm text-gray-600">exporter finding global buyers</div>
                    </div>
                  </div>
                </label>
                <label className="flex items-center space-x-2 flex-1">
                  <input
                    type="radio"
                    name="role"
                    value="landlord"
                    checked={role === 'landlord'}
                    onChange={(e) => setRole(e.target.value as 'tenant' | 'landlord')}
                    className="text-green-600 focus:ring-green-500"
                    disabled={isLoading}
                  />
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <span className="text-green-600 text-sm">🛒</span>
                    </div>
                    <div>
                      <div className="text-sm font-medium">I&apos;m a buyer, trader & importer</div>
                      <div className="text-sm text-gray-600">sourcing from verified suppliers</div>
                    </div>
                  </div>
                </label>
              </div>

              {/* Company Name and Full Name */}
              <div className="grid grid-cols-2 gap-3">
                <Input
                  id="firstName"
                  name="firstName"
                  type="text"
                  placeholder="Company name"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  required
                  disabled={isLoading}
                  className="px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 disabled:opacity-50"
                />
                <div className="flex">
                  <select className="px-3 py-3 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-gray-50">
                    <option>Ms.</option>
                    <option>Mr.</option>
                    <option>Mrs.</option>
                  </select>
                  <Input
                    id="lastName"
                    name="lastName"
                    type="text"
                    placeholder="Full name"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    required
                    disabled={isLoading}
                    className="flex-1 px-4 py-3 border border-l-0 border-gray-300 rounded-r-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 disabled:opacity-50"
                  />
                </div>
              </div>

              {/* Product and Country/Phone */}
              <div className="grid grid-cols-2 gap-3">
                <div className="relative">
                  <Input
                    id="product"
                    name="product"
                    type="text"
                    placeholder="Product you want to sell"
                    disabled={isLoading}
                    className="w-full px-4 py-3 pr-8 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 disabled:opacity-50"
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
                <div className="flex">
                  <select className="px-3 py-3 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-gray-50">
                    <option>Country</option>
                    <option>Vietnam</option>
                    <option>USA</option>
                  </select>
                  <Input
                    id="phone"
                    name="phone"
                    type="tel"
                    placeholder="Phone number"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    required
                    disabled={isLoading}
                    className="flex-1 px-4 py-3 border border-l-0 border-gray-300 rounded-r-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 disabled:opacity-50"
                  />
                </div>
              </div>

              {/* Create Your Login Section */}
              <div className="mt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Create Your Login</h3>

                {/* Email */}
                <div className="mb-4">
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="✉ <EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    disabled={isLoading}
                    className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 disabled:opacity-50"
                  />
                </div>

                {/* Password */}
                <div className="relative">
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    placeholder="••••••••"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    disabled={isLoading}
                    className="w-full px-4 py-3 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 disabled:opacity-50"
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </div>
                </div>

                {/* Confirm Password - Hidden but set to match password */}
                <div className="hidden">
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    placeholder="Xác nhận mật khẩu"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    disabled={isLoading}
                    className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 disabled:opacity-50"
                  />
                </div>
              </div>

              {/* Terms and Conditions */}
              <div className="flex items-start space-x-2 text-xs text-gray-600">
                <input
                  type="checkbox"
                  id="terms"
                  className="mt-1 rounded border-gray-300 text-green-600 focus:ring-green-500"
                  required
                />
                <label htmlFor="terms">
                  By joining, I agree to FreshDi.com <a href="#" className="text-green-600 hover:underline">Terms of Use</a>, <a href="#" className="text-green-600 hover:underline">Privacy Policy</a>, <a href="#" className="text-green-600 hover:underline">Disclaimers</a> and receive emails related to our services.
                </label>
              </div>

              {/* Development Mode Checkbox */}
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="developmentMode"
                  checked={isDevelopmentMode}
                  onChange={(e) => setIsDevelopmentMode(e.target.checked)}
                  className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                />
                <label htmlFor="developmentMode" className="text-sm text-gray-600">
                  Chế độ phát triển (bỏ qua xác thực email)
                </label>
              </div>

              {/* Submit Button */}
              <div className="pt-2">
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full py-3 px-4 bg-green-600 hover:bg-green-700 text-white font-medium rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? "Creating account..." : "Create free account now"}
                </Button>
              </div>

              {/* OR Divider */}
              <div className="relative my-6">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-gray-500">OR</span>
                </div>
              </div>

              {/* Zalo Registration */}
              <div className="text-center">
                <Button
                  type="button"
                  variant="outline"
                  className="w-full py-3 px-4 border border-blue-500 text-blue-500 hover:bg-blue-50 font-medium rounded-md transition-colors flex items-center justify-center space-x-2"
                >
                  <span>📱</span>
                  <span>Đăng ký bằng Zalo</span>
                </Button>
              </div>

              <div className="text-center space-y-1 pt-4">
                <p className="text-sm">
                  Đã có tài khoản?  &nbsp;
                  <a href="/login" className="text-green-600 hover:text-green-500">
                     Đăng nhập
                  </a>
                </p>
              </div>
            </form>
          ) : currentStep === 'verification' ? (
            <form className="space-y-4" onSubmit={handleVerificationSubmit}>
              <div>
                <Input
                  id="verificationCode"
                  name="verificationCode"
                  type="text"
                  placeholder="Nhập mã xác thực 6 số"
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value)}
                  required
                  disabled={isLoading}
                  maxLength={6}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 disabled:opacity-50 text-center text-lg tracking-widest"
                />
              </div>

              <div className="pt-2">
                <Button
                  type="submit"
                  disabled={isLoading || verificationCode.length !== 6}
                  className="w-full py-3 px-4 bg-green-500 hover:bg-green-600 text-white font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? "ĐANG XÁC THỰC..." : "XÁC THỰC"}
                </Button>
              </div>

              <div className="text-center space-y-2 pt-4">
                <p className="text-sm text-gray-600">
                  Không nhận được mã?
                </p>
                <button
                  type="button"
                  onClick={resendVerification}
                  disabled={isLoading}
                  className="text-sm text-green-600 hover:text-green-500 disabled:opacity-50"
                >
                  Gửi lại mã xác thực
                </button>
                <br />
                <button
                  type="button"
                  onClick={() => setCurrentStep('form')}
                  disabled={isLoading}
                  className="text-sm text-gray-600 hover:text-gray-500 disabled:opacity-50"
                >
                  ← Quay lại form đăng ký
                </button>
              </div>
            </form>
          ) : (
            /* Profile Update Form */
            <form className="space-y-4" onSubmit={handleProfileUpdate}>
              {/* Bio */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Giới thiệu bản thân
                </label>
                <textarea
                  id="bio"
                  name="bio"
                  placeholder="Viết vài dòng giới thiệu về bản thân..."
                  value={bio}
                  onChange={(e) => setBio(e.target.value)}
                  disabled={isLoading}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 disabled:opacity-50 resize-none"
                />
              </div>

              {/* Date of Birth and Gender */}
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Ngày sinh
                  </label>
                  <Input
                    id="dateOfBirth"
                    name="dateOfBirth"
                    type="date"
                    value={dateOfBirth}
                    onChange={(e) => setDateOfBirth(e.target.value)}
                    disabled={isLoading}
                    className="px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 disabled:opacity-50"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Giới tính
                  </label>
                  <select
                    value={gender}
                    onChange={(e) => setGender(e.target.value as 'male' | 'female' | 'other')}
                    disabled={isLoading}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 disabled:opacity-50"
                  >
                    <option value="male">Nam</option>
                    <option value="female">Nữ</option>
                    <option value="other">Khác</option>
                  </select>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Đang cập nhật...' : 'Cập nhật thông tin'}
                </Button>

                <Button
                  type="button"
                  variant="outline"
                  onClick={handleSkipProfileUpdate}
                  disabled={isLoading}
                  className="w-full border-gray-300 text-gray-700 hover:bg-gray-50 font-medium py-3 px-4 rounded-lg transition duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Bỏ qua, cập nhật sau
                </Button>
              </div>
            </form>
          )}
          </div>
        </div>
      </div>
    </div>
  )
}
